import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { getLocales } from 'react-native-localize';

// Import translations
import arCommon from '@freela/i18n/locales/ar/common.json';
import arAuth from '@freela/i18n/locales/ar/auth.json';
import arExpert from '@freela/i18n/locales/ar/expert.json';
import arClient from '@freela/i18n/locales/ar/client.json';
import enCommon from '@freela/i18n/locales/en/common.json';

// Combine translations
const arTranslations = {
  ...arCommon,
  ...arAuth,
  ...arExpert,
  ...arClient,
};

const enTranslations = {
  ...enCommon,
};

// Store
import { useAppStore } from '../store/appStore';

// Get device language
const getDeviceLanguage = (): string => {
  const locales = getLocales();
  const deviceLanguage = locales[0]?.languageCode || 'ar';
  
  // Default to Arabic for Syrian users
  return ['ar', 'en'].includes(deviceLanguage) ? deviceLanguage : 'ar';
};

// Initialize i18n
export const initializeI18n = async (): Promise<void> => {
  const { language } = useAppStore.getState();
  const deviceLanguage = getDeviceLanguage();
  const selectedLanguage = language || deviceLanguage;

  await i18n
    .use(initReactI18next)
    .init({
      resources: {
        ar: {
          translation: arTranslations,
        },
        en: {
          translation: enTranslations,
        },
      },
      lng: selectedLanguage,
      fallbackLng: 'ar',
      debug: __DEV__,
      interpolation: {
        escapeValue: false,
      },
      react: {
        useSuspense: false,
      },
    });

  // Update store with selected language
  if (language !== selectedLanguage) {
    useAppStore.getState().setLanguage(selectedLanguage as 'ar' | 'en');
  }
};

// Change language
export const changeLanguage = async (language: 'ar' | 'en'): Promise<void> => {
  await i18n.changeLanguage(language);
  useAppStore.getState().setLanguage(language);
};

export default i18n;
