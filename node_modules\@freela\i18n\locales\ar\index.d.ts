declare const _default: {
    common: {
        navigation: {
            home: string;
            search: string;
            services: string;
            bookings: string;
            messages: string;
            profile: string;
            dashboard: string;
            settings: string;
            help: string;
            logout: string;
        };
        actions: {
            save: string;
            cancel: string;
            delete: string;
            edit: string;
            submit: string;
            back: string;
            next: string;
            previous: string;
            confirm: string;
            retry: string;
            close: string;
            open: string;
            view: string;
            download: string;
            upload: string;
            share: string;
            copy: string;
            search: string;
            filter: string;
            sort: string;
            refresh: string;
        };
        status: {
            loading: string;
            success: string;
            error: string;
            pending: string;
            completed: string;
            cancelled: string;
            active: string;
            inactive: string;
            approved: string;
            rejected: string;
            draft: string;
        };
        time: {
            now: string;
            today: string;
            yesterday: string;
            tomorrow: string;
            thisWeek: string;
            lastWeek: string;
            thisMonth: string;
            lastMonth: string;
            minute: string;
            minutes: string;
            hour: string;
            hours: string;
            day: string;
            days: string;
            week: string;
            weeks: string;
            month: string;
            months: string;
            year: string;
            years: string;
        };
        weekdays: {
            monday: string;
            tuesday: string;
            wednesday: string;
            thursday: string;
            friday: string;
            saturday: string;
            sunday: string;
        };
        months: {
            january: string;
            february: string;
            march: string;
            april: string;
            may: string;
            june: string;
            july: string;
            august: string;
            september: string;
            october: string;
            november: string;
            december: string;
        };
        currency: {
            usd: string;
            syp: string;
            symbol: {
                usd: string;
                syp: string;
            };
        };
        location: {
            governorates: {
                damascus: string;
                aleppo: string;
                homs: string;
                hama: string;
                latakia: string;
                tartus: string;
                idlib: string;
                daraa: string;
                sweida: string;
                quneitra: string;
                raqqa: string;
                deir_ez_zor: string;
                hasaka: string;
                damascus_countryside: string;
            };
        };
        validation: {
            required: string;
            email: string;
            phone: string;
            password: string;
            confirmPassword: string;
            minLength: string;
            maxLength: string;
            min: string;
            max: string;
            numeric: string;
            url: string;
        };
        errors: {
            general: string;
            network: string;
            unauthorized: string;
            forbidden: string;
            notFound: string;
            serverError: string;
            timeout: string;
            offline: string;
        };
        pagination: {
            previous: string;
            next: string;
            first: string;
            last: string;
            page: string;
            of: string;
            showing: string;
            to: string;
            results: string;
        };
        fileUpload: {
            dragDrop: string;
            selectFile: string;
            selectFiles: string;
            maxSize: string;
            allowedTypes: string;
            uploading: string;
            uploadSuccess: string;
            uploadError: string;
            fileTooLarge: string;
            invalidType: string;
        };
    };
    auth: {
        login: {
            title: string;
            subtitle: string;
            email: string;
            password: string;
            rememberMe: string;
            forgotPassword: string;
            loginButton: string;
            noAccount: string;
            signUp: string;
            loginWith: string;
            google: string;
            facebook: string;
            success: string;
            error: string;
        };
        register: {
            title: string;
            subtitle: string;
            firstName: string;
            lastName: string;
            email: string;
            phone: string;
            password: string;
            confirmPassword: string;
            role: string;
            client: string;
            expert: string;
            location: string;
            governorate: string;
            city: string;
            language: string;
            arabic: string;
            english: string;
            acceptTerms: string;
            termsOfService: string;
            and: string;
            privacyPolicy: string;
            registerButton: string;
            haveAccount: string;
            signIn: string;
            success: string;
            emailExists: string;
            weakPassword: string;
        };
        forgotPassword: {
            title: string;
            subtitle: string;
            email: string;
            sendButton: string;
            backToLogin: string;
            success: string;
            emailNotFound: string;
            checkEmail: string;
            emailSent: string;
        };
        resetPassword: {
            title: string;
            subtitle: string;
            newPassword: string;
            confirmPassword: string;
            resetButton: string;
            success: string;
            invalidToken: string;
            passwordsNotMatch: string;
        };
        verification: {
            email: {
                title: string;
                subtitle: string;
                resend: string;
                success: string;
                alreadyVerified: string;
                invalidToken: string;
                resendSuccess: string;
            };
            phone: {
                title: string;
                subtitle: string;
                code: string;
                verify: string;
                resend: string;
                success: string;
                invalidCode: string;
                expired: string;
                resendSuccess: string;
            };
        };
        logout: {
            confirm: string;
            success: string;
        };
        profile: {
            incomplete: string;
            completeProfile: string;
            verificationRequired: string;
            pendingApproval: string;
        };
        security: {
            twoFactor: {
                title: string;
                enable: string;
                disable: string;
                code: string;
                verify: string;
                backup: string;
                success: string;
                disabled: string;
            };
            sessions: {
                title: string;
                current: string;
                device: string;
                location: string;
                lastActive: string;
                terminate: string;
                terminateAll: string;
            };
        };
    };
    expert: {
        profile: {
            title: string;
            createProfile: string;
            editProfile: string;
            personalInfo: string;
            professionalInfo: string;
            portfolio: string;
            services: string;
            reviews: string;
            earnings: string;
            profileTitle: string;
            description: string;
            skills: string;
            experience: string;
            hourlyRate: string;
            availability: string;
            responseTime: string;
            languages: string;
            education: string;
            certifications: string;
            verification: string;
            verified: string;
            unverified: string;
            completionRate: string;
            onTimeDelivery: string;
            rating: string;
            totalEarnings: string;
            activeServices: string;
            completedProjects: string;
        };
        experience: {
            beginner: string;
            intermediate: string;
            expert: string;
            senior: string;
        };
        responseTime: {
            within_hour: string;
            within_day: string;
            within_week: string;
        };
        availability: {
            hoursPerWeek: string;
            timezone: string;
            workingHours: string;
            workingDays: string;
            unavailableDates: string;
            from: string;
            to: string;
        };
        services: {
            myServices: string;
            addService: string;
            editService: string;
            deleteService: string;
            serviceTitle: string;
            serviceDescription: string;
            category: string;
            subcategory: string;
            tags: string;
            images: string;
            pricing: string;
            deliveryTime: string;
            revisions: string;
            requirements: string;
            addOns: string;
            packages: string;
            basicPackage: string;
            standardPackage: string;
            premiumPackage: string;
            packageName: string;
            packageDescription: string;
            packagePrice: string;
            packageDelivery: string;
            packageRevisions: string;
            packageFeatures: string;
            addFeature: string;
            status: string;
            draft: string;
            pending: string;
            active: string;
            paused: string;
            rejected: string;
            views: string;
            orders: string;
            revenue: string;
        };
        bookings: {
            myBookings: string;
            newBooking: string;
            acceptBooking: string;
            rejectBooking: string;
            startWork: string;
            deliverWork: string;
            completeBooking: string;
            cancelBooking: string;
            bookingDetails: string;
            clientRequirements: string;
            deliverables: string;
            revisions: string;
            timeline: string;
            milestones: string;
            payment: string;
            status: {
                pending: string;
                accepted: string;
                in_progress: string;
                delivered: string;
                revision_requested: string;
                completed: string;
                cancelled: string;
                disputed: string;
            };
            acceptanceMessage: string;
            rejectionReason: string;
            deliveryMessage: string;
            deliveryFiles: string;
            estimatedDelivery: string;
            actualDelivery: string;
            daysRemaining: string;
            overdue: string;
        };
        earnings: {
            totalEarnings: string;
            availableBalance: string;
            pendingClearance: string;
            withdrawn: string;
            thisMonth: string;
            lastMonth: string;
            thisYear: string;
            earningsHistory: string;
            withdrawalHistory: string;
            withdraw: string;
            withdrawalMethod: string;
            bankTransfer: string;
            paypal: string;
            minimumWithdrawal: string;
            withdrawalFee: string;
            processingTime: string;
        };
        analytics: {
            overview: string;
            profileViews: string;
            serviceViews: string;
            inquiries: string;
            conversionRate: string;
            responseRate: string;
            averageRating: string;
            repeatClients: string;
            topServices: string;
            topKeywords: string;
            performance: string;
            trends: string;
            period: {
                today: string;
                week: string;
                month: string;
                quarter: string;
                year: string;
            };
        };
        portfolio: {
            myPortfolio: string;
            addProject: string;
            editProject: string;
            deleteProject: string;
            projectTitle: string;
            projectDescription: string;
            projectImages: string;
            projectUrl: string;
            completedDate: string;
            featured: string;
            category: string;
            tags: string;
            client: string;
            duration: string;
            technologies: string;
        };
        verification: {
            title: string;
            subtitle: string;
            idDocument: string;
            addressProof: string;
            professionalCertificate: string;
            portfolio: string;
            uploadDocument: string;
            documentUploaded: string;
            pending: string;
            approved: string;
            rejected: string;
            resubmit: string;
            verificationBenefits: string;
            increasedTrust: string;
            higherRanking: string;
            premiumBadge: string;
            accessToMore: string;
        };
    };
    client: {
        dashboard: {
            title: string;
            welcome: string;
            activeBookings: string;
            completedProjects: string;
            totalSpent: string;
            savedExperts: string;
            recentActivity: string;
            quickActions: string;
            findExperts: string;
            postProject: string;
            viewBookings: string;
            browseServices: string;
        };
        search: {
            title: string;
            searchPlaceholder: string;
            filters: string;
            category: string;
            location: string;
            priceRange: string;
            deliveryTime: string;
            rating: string;
            experienceLevel: string;
            availability: string;
            language: string;
            sortBy: string;
            relevance: string;
            price: string;
            popularity: string;
            newest: string;
            results: string;
            noResults: string;
            tryDifferentKeywords: string;
            clearFilters: string;
            showMore: string;
            expertProfile: string;
            viewProfile: string;
            contactExpert: string;
            saveExpert: string;
            unsaveExpert: string;
        };
        services: {
            serviceDetails: string;
            aboutService: string;
            aboutExpert: string;
            packages: string;
            reviews: string;
            faq: string;
            selectPackage: string;
            basic: string;
            standard: string;
            premium: string;
            price: string;
            deliveryTime: string;
            revisions: string;
            features: string;
            addOns: string;
            requirements: string;
            orderNow: string;
            contactFirst: string;
            addToCart: string;
            comparePackages: string;
            included: string;
            notIncluded: string;
            optional: string;
            popular: string;
            bestValue: string;
            recommended: string;
        };
        booking: {
            title: string;
            serviceDetails: string;
            selectedPackage: string;
            requirements: string;
            additionalInfo: string;
            timeline: string;
            budget: string;
            paymentMethod: string;
            orderSummary: string;
            servicePrice: string;
            addOns: string;
            platformFee: string;
            total: string;
            placeOrder: string;
            termsAgreement: string;
            escrowNotice: string;
            estimatedDelivery: string;
            customInstructions: string;
            attachFiles: string;
            urgentDelivery: string;
            extraRevisions: string;
            sourceFiles: string;
            commercialUse: string;
        };
        bookings: {
            myBookings: string;
            activeBookings: string;
            completedBookings: string;
            cancelledBookings: string;
            bookingDetails: string;
            expert: string;
            service: string;
            status: string;
            orderDate: string;
            deliveryDate: string;
            amount: string;
            viewDetails: string;
            contactExpert: string;
            requestRevision: string;
            approveDelivery: string;
            leaveReview: string;
            downloadFiles: string;
            reportIssue: string;
            cancelBooking: string;
            extendDeadline: string;
            addMilestone: string;
            releaseFunds: string;
            disputeOrder: string;
            orderTimeline: string;
            deliverables: string;
            revisionHistory: string;
            paymentHistory: string;
        };
        reviews: {
            leaveReview: string;
            rating: string;
            reviewTitle: string;
            reviewComment: string;
            whatWentWell: string;
            whatCouldImprove: string;
            wouldRecommend: string;
            yes: string;
            no: string;
            submitReview: string;
            editReview: string;
            deleteReview: string;
            helpful: string;
            notHelpful: string;
            reportReview: string;
            expertResponse: string;
            verifiedPurchase: string;
            anonymous: string;
            publicReview: string;
            privateReview: string;
        };
        messages: {
            conversations: string;
            newMessage: string;
            searchConversations: string;
            markAsRead: string;
            markAsUnread: string;
            deleteConversation: string;
            archiveConversation: string;
            blockUser: string;
            reportUser: string;
            typeMessage: string;
            sendMessage: string;
            attachFile: string;
            sendImage: string;
            voiceMessage: string;
            online: string;
            offline: string;
            lastSeen: string;
            typing: string;
            delivered: string;
            read: string;
            failed: string;
            retry: string;
            messageDeleted: string;
            fileSharing: string;
            maxFileSize: string;
            unsupportedFileType: string;
        };
        payments: {
            paymentMethods: string;
            addPaymentMethod: string;
            creditCard: string;
            debitCard: string;
            paypal: string;
            bankTransfer: string;
            mobileWallet: string;
            cardNumber: string;
            expiryDate: string;
            cvv: string;
            cardholderName: string;
            billingAddress: string;
            saveCard: string;
            defaultPayment: string;
            paymentHistory: string;
            invoices: string;
            downloadInvoice: string;
            refunds: string;
            disputes: string;
            securePayment: string;
            encryptedTransaction: string;
            moneyBackGuarantee: string;
        };
        profile: {
            myProfile: string;
            editProfile: string;
            personalInfo: string;
            contactInfo: string;
            preferences: string;
            security: string;
            notifications: string;
            privacy: string;
            companyInfo: string;
            companyName: string;
            companySize: string;
            industry: string;
            website: string;
            socialMedia: string;
            facebook: string;
            twitter: string;
            linkedin: string;
            instagram: string;
            bio: string;
            interests: string;
            timezone: string;
            language: string;
            currency: string;
            emailNotifications: string;
            smsNotifications: string;
            pushNotifications: string;
            marketingEmails: string;
            weeklyDigest: string;
            projectUpdates: string;
            messageNotifications: string;
            bookingUpdates: string;
        };
    };
};
export default _default;
//# sourceMappingURL=index.d.ts.map